# **自动化文档生成工具 - 产品需求文档 (PRD)**

**技术栈**: **Rust + Slint**
**目标**: 构建一个高效、安全、跨平台的自动化文档批量生成桌面应用，提供原生用户体验。

---

## 1. 产品概述 (Product Overview)

本产品是一款桌面应用程序，旨在解决重复性、大批量的文档生成任务。用户通过可视化的界面，将结构化数据（如CSV/Excel）与Word文档模板进行关联，即可一键、批量生成内容各异但格式统一的文档。我们选用`Rust + Slint`技术栈，以确保应用具备卓越的性能、内存安全以及真正的跨平台原生体验。

### 1.1. 核心价值 (Core Value)

*   **效率提升**: 将数小时的手动填表工作缩短至几分钟，实现生产力飞跃。
*   **零错误率**: 消除人工操作中常见的复制、粘贴错误，保证数据准确性。
*   **品牌一致性**: 确保所有输出文档的格式、字体、Logo等元素完全统一。
*   **规模化处理**: 轻松应对从几十份到数千份文档的批量生成需求。

---

## 2. 核心概念 (Core Concepts)

*   **工作空间 (Workspace)**: 用户隔离不同业务场景的顶层容器，包含多个项目。
*   **项目 (Project)**: 定义一个完整的文档生成任务，封装了数据源、模板、映射规则和输出设置。项目配置以 `.json` 格式保存，便于复用和分享。
*   **数据源 (Data Source)**: 提供填充数据的结构化文件，目前支持 CSV 和 Excel (.xlsx)。
*   **文档模板 (Template)**: 带有占位符的 Word (.docx) 文档。
*   **占位符 (Placeholder)**: 模板中待替换的变量，格式为 `{{字段名}}`。
*   **字段映射 (Field Mapping)**: 建立数据源列（字段）与模板占位符之间对应关系的核心步骤。
*   **生成任务 (Generation Task)**: 一次具体的文档生成执行过程，包含进度、日志和结果。

---

## 3. 功能模块设计 (Functional Modules)

### 3.1. 项目与工作空间管理 (P0 - 核心)

*   **项目创建与管理**:
    *   提供向导式流程创建新项目（命名、描述）。
    *   支持项目的导入、导出（单个 `.json` 配置文件）。
    *   在工作空间侧边栏中以列表或树状视图展示所有项目，支持搜索和快速访问。
*   **配置验证**: 实时检查项目配置（如数据源是否存在、模板是否加载）的完整性，并在UI上给予明确状态提示。

### 3.2. 数据源管理 (P0 - 核心)

*   **文件导入**:
    *   支持拖拽或点击选择导入 CSV 和 Excel (.xlsx) 文件。
    *   **智能解析**: 自动检测CSV文件的编码（UTF-8, GBK）和分隔符。对于Excel，支持选择特定工作表。
*   **数据预览与预处理**:
    *   以表格形式清晰展示导入的数据，支持分页和列宽调整。
    *   **表头识别**: 自动将第一行作为表头，并允许用户手动指定。
    *   提供每列的数据统计摘要（总行数、非空值数量）。

### 3.3. 模板管理 (P0 - 核心)

*   **模板导入**: 支持导入 Microsoft Word (.docx) 格式的文档模板。
*   **占位符智能识别**:
    *   精确解析模板中所有 `{{字段名}}` 格式的占位符。
    *   **鲁棒性**: 能正确处理被Word复杂格式（如分行、不同字体）分割的占位符。
*   **模板预览**:
    *   在界面中预览模板内容，并高亮所有已识别的占位符。
    *   以列表形式展示所有占位符，便于后续映射。

### 3.4. 可视化字段映射 (P0 - 核心)

*   **映射界面**:
    *   采用直观的双栏布局：左侧为数据源字段，右侧为模板占位符。
    *   通过拖拽连线的方式建立映射关系。
*   **智能辅助映射 (P1 - 重要)**:
    *   **自动匹配**: 基于名称相似度（如忽略大小写、下划线）自动尝试匹配。
    *   **匹配建议**: 为未匹配的字段提供最可能的匹配选项。
*   **规则管理**:
    *   实时验证映射，高亮显示未映射的占位符。
    *   允许为未映射的占位符设置默认值（如 "N/A"）或选择留空。

### 3.5. 文档生成引擎 (P0 - 核心)

*   **输出配置**:
    *   **动态命名规则**: 支持使用数据字段、日期、序号等变量自定义输出文件名。示例: `{{合同编号}}-{{客户名称}}.docx`。
    *   **动态路径**: 支持使用数据字段创建多级输出目录。示例: `输出目录/{{年份}}/{{部门}}/`。
*   **批量生成**:
    *   **并行处理**: 利用多线程并行生成文档，最大化利用CPU资源，提升大规模任务的处理速度。
    *   **任务监控**: 实时显示生成进度条、已完成/失败数量和预计剩余时间。
*   **错误处理机制**:
    *   单个文件生成失败（如因数据格式问题）不中断整个任务。
    *   所有失败项将被记录在日志中，便于用户排查和后续处理。

### 3.6. 高级功能 (P1 - 重要)

*   **PDF格式输出**: 在生成Word文档的同时，提供一个选项，将其无损转换为PDF格式。
*   **数据验证规则**: 允许用户为特定数据列配置简单的验证规则（如非空、数字格式），并在数据预览中标示出问题数据。
*   **项目模板库**: 内置常用项目模板（如劳动合同、获奖证书），用户可一键选用，快速开始。

---

## 4. 用户界面与交互设计 (UI/UX)

### 4.1. 整体布局

采用经典的三栏式布局，确保信息架构清晰，操作路径最短。

```
┌─────────────────────────────────────────────────────────────┐
│                        标题栏 / 菜单栏                        │
├─────────────┬────────────────────────────────┬──────────────┤
│             │                                │              │
│ 项目列表     │         主工作区 (核心操作)      │   属性/配置面板 │
│ (侧边栏)     │   (数据预览/模板/映射/输出)      │   (上下文相关)  │
│             │                                │              │
├─────────────┴────────────────────────────────┴──────────────┤
│                        状态栏 + 日志入口                      │
└─────────────────────────────────────────────────────────────┘
```

### 4.2. 设计原则

*   **直观性**: 功能和操作符合用户直觉，减少学习成本。拖拽、点击等交互应自然流畅。
*   **即时反馈**: 用户的任何操作（如文件导入、映射连接）都应有即时的视觉反馈。
*   **引导式操作**: 对于核心流程（项目创建、映射），提供清晰的步骤引导。
*   **一致性**: 整体界面风格、图标、颜色和交互模式保持统一。
*   **无障碍**: 支持键盘导航，确保关键功能可通过键盘访问。

### 4.3. 界面特性

*   **响应式布局**: 窗口大小可调，最小支持 `1280x800` 分辨率。
*   **主题支持**: 内置浅色和深色两种主题，并支持跟随系统设置。
*   **多语言**: 框架层面支持国际化，首期实现中文和英文。

---

## 5. 技术架构设计 (Technical Architecture)

### 5.1. 技术栈选型

*   **核心框架**: **Rust** 作为主要编程语言，保证内存安全和高性能。
*   **GUI框架**: **Slint** 作为UI工具包。它提供了一套声明式的UI描述语言和强大的Rust API，能编译成高性能的原生UI，避免了WebView的性能和资源开销。
*   **异步运行时**: **Tokio** 用于管理并发任务，特别是文件I/O和并行文档生成。
*   **核心依赖库**:
    *   **数据处理**: `calamine` (Excel), `csv` (CSV), `serde` (序列化/反序列化)。
    *   **文档处理**: `docx-rs` (Word文档读写)。
    *   **PDF生成 (P1)**: `genpdf` 或 `printpdf` (原生Rust PDF库)。

### 5.2. 分层架构

我们将采用清晰的分层架构，实现UI、状态管理和业务逻辑的解耦。

```
┌─────────────────────────────────────────────────────────────┐
│                      UI Layer (Slint)                       │
│       (负责界面元素的渲染、布局和基本的用户输入事件)          │
└─────────────────────────────┬───────────────────────────────┘
                              │ (双向数据绑定, 事件回调)
┌─────────────────────────────▼───────────────────────────────┐
│           View-Model / State Management Layer               │
│ (持有UI状态, 响应UI事件, 调用业务逻辑, 将结果暴露给UI)        │
└─────────────────────────────┬───────────────────────────────┘
                              │ (调用服务, 处理业务)
┌─────────────────────────────▼───────────────────────────────┐
│             Business Logic / Service Layer                  │
│   (ProjectManager, DataProcessor, TemplateParser, Generator)  │
└─────────────────────────────┬───────────────────────────────┘
                              │ (读写文件)
┌─────────────────────────────▼───────────────────────────────┐
│                    Data Access Layer (DAL)                    │
│        (负责所有文件系统的读写操作, 如配置、数据、模板)       │
└─────────────────────────────────────────────────────────────┘
```

### 5.3. 核心模块职责

*   **UI Layer (Slint)**: 使用 `.slint` 标记语言定义界面组件。它只负责“展示”，不包含任何业务逻辑。
*   **View-Model Layer**: 作为UI和业务逻辑的桥梁。它从业务层获取数据，处理成UI可直接消费的格式（如列表模型），并通过Slint的属性绑定机制更新UI。同时，它将UI事件（如按钮点击）转换为对业务层服务的调用。
*   **Business Logic Layer**: 应用的核心，包含所有与业务相关的无状态服务。例如，`DataProcessor` 负责解析文件，`Generator` 负责执行文档生成算法。此层完全独立于UI。
*   **Data Access Layer**: 封装所有对文件系统的访问，提供统一、健壮的文件读写接口。

---

## 6. 质量与非功能性需求 (NFRs)

### 6.1. 可靠性与稳定性

*   **数据安全**: 所有用户数据和文档处理均在本地完成，无任何数据上传云端。
*   **格式保真**: 必须100%保留原始 `.docx` 模板中的所有样式、格式、页眉页脚、图片和表格。
*   **健壮性**: 应用程序在处理超大文件（如10万行数据）或异常数据时应保持稳定，不应崩溃，并能给出清晰的错误提示。

### 6.2. 性能要求

*   **启动速度**: 冷启动时间应小于3秒。
*   **UI响应**: 界面交互（如拖拽、滚动）应流畅，响应时间小于100毫秒。
*   **处理效率**: 在标准PC配置下（4核CPU, 8GB RAM），处理1000份文档（每份包含20个占位符）的时间应在1分钟以内。

### 6.3. 兼容性

*   **操作系统**:
    *   Windows 10/11 (x64)
    *   macOS 11+ (Apple Silicon & Intel)
    *   Linux (主流发行版，如 Ubuntu 20.04+)
*   **文件格式**:
    *   Word: Microsoft Office 2007及以上版本的 `.docx` 文件。
    *   Excel: `.xlsx` 格式。

### 6.4. 安全性

*   **本地优先**: 严格遵守本地化原则，不发起任何非必要网络连接。
*   **输入验证**: 对所有用户输入（特别是文件路径）进行严格验证，防止路径遍历等攻击。
*   **依赖安全**: 定期审查并更新所有第三方依赖库，规避已知的安全漏洞。

---

## 7. 总结 (Summary)

本产品旨在成为同类工具中的佼佼者，为用户提供一个功能强大、体验一流的自动化文档解决方案。

*   **技术优势**:
    *   **内存安全与高性能**: 基于Rust语言构建，从根本上保证了应用的安全和效率。
    *   **原生UI体验**: 采用Slint构建原生GUI，响应速度快，资源占用低，无WebView的额外开销。
    *   **真正跨平台**: 一套核心代码库，可编译到所有主流桌面平台，保证功能和体验的一致性。
*   **核心竞争力**:
    *   极致的批量处理性能。
    *   智能、易用的可视化字段映射系统。
    *   对复杂Word模板格式的完美兼容。
    *   安全可靠的纯本地化数据处理。
